from rest_framework import status
from rest_framework.views import exception_handler
from rest_framework.response import Response


class UserException(Exception):
    default_message = "An error occurred with user operation"
    default_code = "USER_ERROR"
    status_code = status.HTTP_400_BAD_REQUEST

    def __init__(self, message=None, code=None, status_code=None):
        self.message = message or self.default_message
        self.code = code or self.default_code
        self.status_code = status_code or self.status_code
        super().__init__(self.message)


class UserAlreadyExistsException(UserException):
    default_message = "User already exists"
    default_code = "USER_ALREADY_EXISTS"
    status_code = status.HTTP_409_CONFLICT


class UserNotFoundException(UserException):
    default_message = "User not found"
    default_code = "USER_NOT_FOUND"
    status_code = status.HTTP_404_NOT_FOUND
class UserPasswordIsWrong(UserException):
    default_message=""
    default_code=""
    status_code=status.HTTP_400_BAD_REQUEST

class InvalidUserDataException(UserException):
    default_message = "Invalid user data provided"
    default_code = "INVALID_USER_DATA"
    status_code = status.HTTP_400_BAD_REQUEST


class UserValidationException(UserException):
    default_message = "User validation failed"
    default_code = "USER_VALIDATION_ERROR"
    status_code = status.HTTP_400_BAD_REQUEST


class FileUploadException(UserException):
    default_message = "File upload failed"
    default_code = "FILE_UPLOAD_ERROR"
    status_code = status.HTTP_400_BAD_REQUEST


def custom_exception_handler(exc, context):
    response = exception_handler(exc, context)
    if isinstance(exc, UserException):
        custom_response_data = {
            'error': {
                'code': exc.code,
                'message': exc.message,
                'details': str(exc)
            },
            'success': False
        }
        return Response(custom_response_data, status=exc.status_code)

    return response
